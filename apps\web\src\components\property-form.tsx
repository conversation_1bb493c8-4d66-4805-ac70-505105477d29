"use client"

import { useState } from "react"
import { useActionState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Checkbox } from "@/components/ui/checkbox"
import { Building, Plus } from "lucide-react"
import { createProperty } from "@/lib/actions"

const currencies = ["USD", "EUR", "GBP", "CAD", "AUD"]

export function PropertyForm() {
  const [state, formAction] = useActionState(createProperty, null)
  const [isVacant, setIsVacant] = useState(true)

  return (
    <Card className="max-w-4xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Building className="w-5 h-5" />
          Add Property
        </CardTitle>
        <CardDescription>Add a new rental property to your portfolio</CardDescription>
      </CardHeader>
      <CardContent>
        <form action={formAction} className="space-y-6">
          {/* Basic Information */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Basic Information</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="property_name">Property Name</Label>
                <Input id="property_name" name="property_name" placeholder="e.g., Sunset Apartments" required />
                {state?.errors?.property_name && (
                  <p className="text-sm text-red-500">{state.errors.property_name[0]}</p>
                )}
              </div>
              <div className="space-y-2">
                <Label htmlFor="plot_number">Plot Number</Label>
                <Input id="plot_number" name="plot_number" placeholder="e.g., 123" required />
                {state?.errors?.plot_number && <p className="text-sm text-red-500">{state.errors.plot_number[0]}</p>}
              </div>
            </div>
          </div>

          {/* Location */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Location</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="street_name">Street Name</Label>
                <Input id="street_name" name="street_name" placeholder="e.g., Main Street" required />
                {state?.errors?.street_name && <p className="text-sm text-red-500">{state.errors.street_name[0]}</p>}
              </div>
              <div className="space-y-2">
                <Label htmlFor="city">City</Label>
                <Input id="city" name="city" placeholder="e.g., New York" required />
                {state?.errors?.city && <p className="text-sm text-red-500">{state.errors.city[0]}</p>}
              </div>
              <div className="space-y-2">
                <Label htmlFor="state">State</Label>
                <Input id="state" name="state" placeholder="e.g., NY" required />
                {state?.errors?.state && <p className="text-sm text-red-500">{state.errors.state[0]}</p>}
              </div>
              <div className="space-y-2">
                <Label htmlFor="country">Country</Label>
                <Input id="country" name="country" placeholder="e.g., USA" required />
                {state?.errors?.country && <p className="text-sm text-red-500">{state.errors.country[0]}</p>}
              </div>
            </div>
          </div>

          {/* Property Details */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Property Details</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="bedrooms">Bedrooms</Label>
                <Input id="bedrooms" name="bedrooms" type="number" min="0" defaultValue="0" />
                {state?.errors?.bedrooms && <p className="text-sm text-red-500">{state.errors.bedrooms[0]}</p>}
              </div>
              <div className="space-y-2">
                <Label htmlFor="bathrooms">Bathrooms</Label>
                <Input id="bathrooms" name="bathrooms" type="number" min="0" defaultValue="0" />
                {state?.errors?.bathrooms && <p className="text-sm text-red-500">{state.errors.bathrooms[0]}</p>}
              </div>
            </div>
          </div>

          {/* Financial Information */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Financial Information</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="space-y-2">
                <Label htmlFor="base_rent">Base Rent</Label>
                <Input id="base_rent" name="base_rent" type="number" min="0" step="0.01" placeholder="0.00" />
                {state?.errors?.base_rent && <p className="text-sm text-red-500">{state.errors.base_rent[0]}</p>}
              </div>
              <div className="space-y-2">
                <Label htmlFor="base_deposit">Base Deposit</Label>
                <Input id="base_deposit" name="base_deposit" type="number" min="0" step="0.01" placeholder="0.00" />
                {state?.errors?.base_deposit && <p className="text-sm text-red-500">{state.errors.base_deposit[0]}</p>}
              </div>
              <div className="space-y-2">
                <Label htmlFor="currency">Currency</Label>
                <Select name="currency" defaultValue="USD">
                  <SelectTrigger>
                    <SelectValue placeholder="Select currency" />
                  </SelectTrigger>
                  <SelectContent>
                    {currencies.map((currency) => (
                      <SelectItem key={currency} value={currency}>
                        {currency}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {state?.errors?.currency && <p className="text-sm text-red-500">{state.errors.currency[0]}</p>}
              </div>
            </div>
          </div>

          {/* Additional Information */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Additional Information</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="listing_date">Listing Date</Label>
                <Input
                  id="listing_date"
                  name="listing_date"
                  type="date"
                  defaultValue={new Date().toISOString().split("T")[0]}
                  required
                />
                {state?.errors?.listing_date && <p className="text-sm text-red-500">{state.errors.listing_date[0]}</p>}
              </div>
              <div className="flex items-center space-x-2 pt-8">
                <Checkbox id="vacant" name="vacant" checked={isVacant} onCheckedChange={setIsVacant} />
                <Label htmlFor="vacant">Property is currently vacant</Label>
              </div>
            </div>
          </div>

          {/* Success/Error Messages */}
          {state?.success && (
            <div className="p-4 bg-green-50 border border-green-200 rounded-md">
              <p className="text-green-800">Property created successfully!</p>
            </div>
          )}
          {state?.error && (
            <div className="p-4 bg-red-50 border border-red-200 rounded-md">
              <p className="text-red-800">{state.error}</p>
            </div>
          )}

          <Button type="submit" className="w-full">
            <Plus className="w-4 h-4 mr-2" />
            Add Property
          </Button>
        </form>
      </CardContent>
    </Card>
  )
}
